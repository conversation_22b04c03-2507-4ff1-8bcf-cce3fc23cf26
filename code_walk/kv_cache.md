# Prefix Caching

```python
class KVCacheStorage:
    def put(self, tokens, kv_cache_tensor):
        pass
    
    def get(self, tokens) -> kv_cache_tensor:
        pass
```
和传统的KV不一样的是它有一个 `prefix-based matching` 的概念。
Tokens 1: ABCDE -> [KV1, KV2, KV3, KV4, KV5]
Tokens 2: ABCDF -> [KV1, KV2, KV3, KV4, KV6]

kv_cache_store.put("ABCDE", [KV1, KV2, KV3, KV4, KV5])")
kv_cache_store.put("ABCDF", [KV1, KV2, KV3, KV4, KV6])

它是一个经典的字符串最长前缀匹配问题。典型的做法是用 Trie 树来做，但是实现比较复杂，所以要简化实现：
"ABCDEF" -> "AB","CD","EF" -> list of chunked prefix hashes (核心思想就是把一个长的字符串分成几个小的字符串，然后用hash来表示这个小的字符串)
```python
prefix_hash = ""
for chunk in chunked_tokens: #["AB","CD","EF"],把key 分成 chunk 块，再来计算hash
    chunk_hash = hash(prefix_hash + chunk)
    prefix_hash = chunk_hash

# 比如后面对接一个redis
# ? chunked_hashes 和 chunked_kv 是什么，为什么需要进过zip处理
for chunk_hash,chunk_kv in zip(chunked_hashes, chunked_kv):
    redis.put(chunk_hash, chunk_kv)

for chunk_hash in chunked_hashes:
    redis.get(chunk_hash)
```

LMCache 就是基于这个顶级抽象实现的，可以去参考对应的代码。


核心代码在：`get_computed_blocks` vllm/v1/core/sched/scheduler.py
缓存驱逐的时候：`_maybe_evict_cached_block` vllm/v1/core/block_pool.py
```python

1. block_size 默认值问题？
2. 