# vllm/v1/core/sched 包分析

vllm/v1/core/sched 包是 vLLM 系统中负责请求调度和资源分配的核心模块。它决定哪些请求在何时执行，以及如何最有效地利用计算资源。以下是对该包中各文件的分析及其数据流程。

## 文件功能分析

### 1. interface.py

- **功能**：定义调度器的抽象接口
- **核心类**：`SchedulerInterface`（抽象基类）
- **主要职责**：
  - 定义调度器必须实现的方法
  - 提供统一的接口规范，确保所有调度器实现都满足基本要求
- **关键方法**：
  - `schedule()`: 生成调度决策，确定本轮迭代处理哪些请求
  - `update_from_output()`: 根据模型输出更新调度器状态
  - `add_request()`: 添加新请求到调度队列
  - `finish_requests()`: 处理完成或中止的请求
  - `reset_prefix_cache()`: 重置前缀缓存

### 2. output.py

- **功能**：定义调度器输出的数据结构
- **核心类**：
  - `NewRequestData`: 新请求的数据封装
  - `CachedRequestData`: 已缓存请求的数据封装
  - `SchedulerOutput`: 调度器输出的完整结构
- **主要职责**：
  - 将调度决策封装为结构化数据
  - 提供在调度器和模型执行器之间传递信息的数据格式
- **关键字段**：
  - `scheduled_new_reqs`: 首次调度的请求列表
  - `scheduled_cached_reqs`: 已被调度过的请求列表
  - `num_scheduled_tokens`: 每个请求被分配的token数
  - `structured_output_request_ids`: 使用结构化输出的请求ID映射
  - `grammar_bitmask`: 用于结构化输出的语法位掩码

### 3. scheduler.py

- **功能**：调度器的核心实现
- **核心类**：`Scheduler`
- **主要职责**：
  - 实现 `SchedulerInterface` 定义的所有方法
  - 维护请求队列并进行优先级调度
  - 分配KV缓存和编码器资源
  - 处理请求的生命周期（等待、运行、完成、抢占）
  - 支持投机解码和结构化输出
- **关键方法**：
  - `schedule()`: 产生调度决策
  - `update_from_output()`: 处理模型输出并更新请求状态
  - `_try_schedule_encoder_inputs()`: 处理编码器输入的调度
  - `_make_cached_request_data()`: 创建缓存请求数据

### 4. utils.py

- **功能**：提供辅助工具函数
- **核心函数**：`check_stop()`
- **主要职责**：
  - 检查请求是否应该停止生成
  - 处理各种停止条件：达到最大长度、生成EOS、碰到停止token等

## 调度器数据流程

vllm/v1/core/sched 包的数据流程主要围绕请求调度和资源分配展开。以下是整个流程的详细分析：

### 1. 请求入口和初始化

1. **请求添加**：
   - 外部调用 `scheduler.add_request(request)` 添加新请求
   - 请求被放入等待队列 `self.waiting`
   - 请求初始状态为 `RequestStatus.WAITING`

2. **调度器初始化**：
   - 初始化 `KVCacheManager` 和 `EncoderCacheManager`
   - 设置调度约束：最大请求数、最大token数、模型最大长度等
   - 配置投机解码参数 (如使用EAGLE时)

### 2. 调度决策过程

每个迭代循环中，`schedule()` 方法执行以下步骤：

1. **资源预算设置**：
   - 设置token预算 `token_budget = self.max_num_scheduled_tokens`
   - 设置编码器预算 `encoder_budget = self.max_num_encoder_input_tokens`

2. **优先调度运行中请求**：
   - 首先处理已在运行状态的请求 (`self.running`)
   - 对每个请求计算需要处理的新token数量
   - 尝试分配KV缓存和编码器资源

3. **请求抢占机制**：
   - 如果资源不足，低优先级请求会被抢占
   - 被抢占请求状态变为 `RequestStatus.PREEMPTED`
   - 被抢占请求被移回等待队列前端

4. **处理等待队列**：
   - 在处理完运行中请求后，尝试调度等待队列中的请求
   - 检查前缀缓存命中，利用已计算的缓存块
   - 为新请求分配资源并将其状态改为 `RequestStatus.RUNNING`

5. **结构化输出处理**：
   - 对需要结构化输出的请求，准备语法位掩码
   - 跳过等待FSM编译的请求

6. **LoRA处理**：
   - 确保同时运行的LoRA请求数不超过限制
   - 跟踪已调度的LoRA ID集合

### 3. 调度结果生成

1. **生成调度输出**：
   - 创建 `NewRequestData` 对象列表（新请求）
   - 创建 `CachedRequestData` 对象列表（恢复请求和继续运行请求）
   - 填充token分配和资源使用信息
   - 生成 `SchedulerOutput` 对象

2. **资源分配信息**：
   - `num_scheduled_tokens`: 记录每个请求分配的token数
   - `scheduled_encoder_inputs`: 记录需要编码器处理的输入
   - `num_common_prefix_blocks`: 所有请求的共同前缀块数

3. **投机解码信息**：
   - `scheduled_spec_decode_tokens`: 记录投机token信息
   - 处理EAGLE等投机解码方法所需的资源

### 4. 模型输出处理

调用 `update_from_output()` 处理模型执行结果：

1. **处理生成的token**：
   - 将模型生成的token添加到请求的输出中
   - 使用 `check_stop()` 检查停止条件
   - 更新请求状态（运行中、完成等）

2. **处理投机解码结果**：
   - 处理接受和拒绝的投机token
   - 调整请求的计算token数

3. **资源释放**：
   - 释放已完成请求的KV缓存和编码器缓存资源
   - 维护 `finished_req_ids` 集合

4. **输出生成**：
   - 创建 `EngineCoreOutput` 对象列表
   - 打包输出返回给引擎核心

### 5. 资源管理流程

1. **KV缓存管理**：
   - 通过 `kv_cache_manager` 分配和释放缓存块
   - 处理前缀缓存命中和复用

2. **编码器缓存管理**：
   - 通过 `encoder_cache_manager` 管理多模态输入的缓存
   - 确保在编码器资源预算内分配资源

3. **请求队列管理**：
   - 维护 `waiting` 和 `running` 队列
   - 处理请求的优先级和抢占关系

## 调度核心算法

### 1. 通用调度算法

vLLM v1 采用统一的调度框架，不再区分"prefill"和"decode"阶段：

```
每个请求维护：
- num_computed_tokens：已计算的token数
- num_tokens_with_spec：提示tokens + 已生成tokens + 投机tokens

调度目标：
- 每步使num_computed_tokens追赶num_tokens_with_spec
- 在token预算和资源约束下最大化吞吐量
```

这种设计支持多种优化：分块预填充、前缀缓存、投机解码等。

### 2. 资源分配策略

```
前缀缓存利用：
1. 计算请求tokens的哈希值
2. 查找最长的缓存命中前缀
3. 只计算未缓存的部分

KV缓存分配：
1. 优先分配给运行中请求
2. 资源不足时执行请求抢占
3. 剩余资源分配给等待队列

编码器资源分配：
1. 只为当前步骤需要的多模态输入分配资源
2. 缓存已处理的编码器输出
```

### 3. 请求优先级策略

```
优先级顺序：
1. 运行中请求（已进入运行队列）
2. 被抢占的请求（优先返回运行队列）
3. 等待队列中的请求（FIFO顺序）

抢占策略：
- 资源不足时，抢占运行队列中最低优先级的请求
- 被抢占请求放回等待队列前端
```

## 关键优化技术

1. **对象池复用**：
   - 使用 `_cached_reqs_data` 缓存 `CachedRequestData` 对象
   - 减少频繁创建和销毁对象的开销

2. **前缀缓存利用**：
   - 利用已计算的KV缓存，避免重复计算
   - 支持不同请求间共享前缀缓存

3. **分块处理**：
   - 使用 `long_prefill_token_threshold` 限制每次处理的token数
   - 允许长请求分多轮处理，提高调度灵活性

4. **多模态优化**：
   - 编码器缓存复用，避免重复处理相同输入
   - 精确调度仅当前步骤需要的编码器输入

5. **投机解码支持**：
   - 为投机token预留资源
   - 处理token接受和拒绝情况

vllm/v1/core/sched 包的设计体现了对大语言模型推理优化的深入思考，通过灵活的调度和高效的资源管理，实现了高吞吐量和低延迟的推理服务。
