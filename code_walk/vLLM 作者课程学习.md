## EP0

https://www.bilibili.com/video/BV1RbQgYsEnK?spm_id_from=333.788.videopod.sections&vd_source=241d03f42fc1d939ead21f8806e503de


1. 模型和显存占用关系？
    1. 简单理解就是 xB → 2x GB， 比如 llama-8b → 16gB 显存，llama-1b → 2GB 显存
        1. 2x 是因为用的 bf 16， 占用 2bytes
    2. Quantization：可以进行量化为bf8,只占用 1bytes，那关系就是 xB → x GB, 比如 llama-8b → 8gB 显存
    3. KVCache: 除了模型本身占用之外，还有kvcache需要，
    
    根据经验一个模型大小占用显存最好在 2/3 以下。
    
    ### 大规模vLLM部署中的重难点
    
    - Kubernetes + 容器化部署
        - vLLM 官方 docker 镜像
        - 实战：helm chart 部署（vllm-project/production-stack）
            - 本地集群
            - 云端集群
        - CPU/内存/GPU如何分配
    - vLLM 状态监测
        - /health 及 /metrics 端口
        - 核心 metrics 监测试
            - num_waiting_request (排队的request)
            - num_runnings_requests (运行的requests数量，这个值会随着集群压力越大，值越大)
            - SLO related about llm
                - 首token （time_to_first_token）节点压力大就会变大
                - 生成token 速度(request_generation_tokens)
                - 端到端的延迟（e2e_request_latency_seconds）
    - 负载均衡和路由算法
        - 负载均衡：如何判断 overload
        - 实战中陆游算法实例（prebal：https://github.com/WukLab/preble）
            - Round-robin
            - Session-based
            - 最大前缀匹配
    - 自动扩容/缩容
        - 实例： Kubernetes HPA + Prometheus Adapter
        - 何时触发？（metrics）
    - 容灾处理（核心是有状态，所以节点的变化会导致有问题，是一个重难点）
        - 业务层异常捕获
        - 动态更新路由
        - 重新发送请求
    
## EP1
vLLM 代码库模块

### Entrypoint (LLM,openapi server, cli)
- llm.py 文件是直接调用 LLMEngine class
- openapi_server.py 是直接调用 AsyncLLMEngine class，然后 AsyncLLMEngine 再调用 LLMEngine
- cli包下面的就是对 openai 这个包的命令行封装。比如
  - `vllm serve facebook/opt-1.3b`
  - `vllm -m vllm.entrypoints.cli.main serve facebook/opt-1.3b`
  - `vllm -m vllm.entrypoints.api_server --model facebook/opt-1.3b`
![img.png](./image/img_6.png)

### LLMEngine
- engine 模块的核心
- core 里面核心就是 scheduler.py, 

### Scheduler ----- batch
- vllm/core/scheduler.py
核心是打包（batch），但是不能用传统方式打包，paged attention 核心是把东西切成了块，存的东西就是kv cache，kv cache manager 就是高效管理kv cache的


### kv cache manager
- vllm/core/block_manager.py
- vllm/core/evictor.py
- Paged Attention

#### evictor.py（驱逐，就是缓存不够用了，要进行驱除）



### Worker （真正干活的）
- vllm/worker/worker_base.py 是一个work的抽象
- vllm/worker/worker.py 就是一个GPU干活的work


### Model executor(Model runner) 是真正执行模型的地方，work会调用Model executor
- vllm/model_executor
- 

### Modelling
- model runner 最核心的读这个文件就行 vllm/model_executor/models/llama.py 最核心就是forward函数


### Attention backend
- FlashAttention(难度过大，可以后续再了解)


## EP2
### Distributed intference
- 1. 为什么要分布式推理: 一张卡放不下
- 2. 分布式推理的类型
  - TP: tensor parallelism
    - PP: pipeline parallelism
    - EP: embedding parallelism

P/D 分离代码核心路径：vllm/distributed/parallel_state.py （get_tp_group）

因为pytorch设计导致PP和TP必须在一起考虑，所以看代码的时候要注意；它的代码就是 GroupCoordinator

GroupCoordinator：
- rank: PP * TP数量，比如PP事4，TP是2，那就是8
- ranks: List[int] ranks就是在一个组里面除自己之外的其它rank，TP为2就是2个rank,PP为4就是有4个rank，但是因为pytorch的特性导致有8个rank。
- world_size: int  # size of the grou
- local_rank: 记录当前rank对应的GPU Device UUID
- cpu_group: cpu 通信，缺点就是慢
- device_group: gpu 通信，对应的就是 Scale out（NVLink） 和 Scale up （IB和Roc、RDMA）

Communication library: vllm/distributed/device_communicators
- GPU：`Pynccl`: communication for nvidia
- CPU：`shared memory`: 共享内存(这应该就是shm)
- `custom allreuce`: A kernel just for all reduce operation
- `torch.distributed`: pytorch的分布式通信

上面的都是Infra 层的通信，下面讲算法层的通信

Algorithm-side(算法通信支持TP)：
- [TP]
- vllm/model_executor/models/llama.py(LlamaAttention)


Pipeline parallel:
- PP 不会优化Request延迟，TP才能降低延时
- self.start_layer --> self.end_layer 就是正在运行的layer
- between workers：communicate IntermediateTensor
- `vllm/worker/model_runner.py`: search `get_pp_group()`

Tp is for attention, PP is for linear layer.

Expert Parallel(专家并行):
- （这个比较难，先不管）

Data Parallel:
- 原因是：TP数量远远小于 EP 的数量，
- 难度也比较大




## EP3
1. attention 不需要通信，在liner layer 层计算的时候需要做一次 all gather 计算通信


### PD Disaggregation

- What's Prefill and Decode
  - prefill — process input prompt, generate KV cache
  - decode — generate tokens based on the KV cache
- Why PD Disaggregation
  - Prefill: attention - N token, QKV -- generate kv cache, takes a long time.
  - Decode: attention N KV, 1Q -generate a new token very fase.
- Problem: prefill will stop other request's decode.
- Solution: PD disaggregation, chunked prefill


Prefill 计算过程，可以看 `llama.py` 中的这段代码：
```
def forward(
        self,
        positions: torch.Tensor,
        hidden_states: torch.Tensor,
    ) -> torch.Tensor:
        qkv, _ = self.qkv_proj(hidden_states)
        q, k, v = qkv.split([self.q_size, self.kv_size, self.kv_size], dim=-1)
        q, k = self.rotary_emb(positions, q, k)
        attn_output = self.attn(q, k, v)
        output, _ = self.o_proj(attn_output)
        return output
```


- PD的三个核心问题
    - How to transfer KV Cache?
      Anwser: 2 modes: (LMCache(2中都有), MoonCake(pooling mode), NIXL(p2p model))
      - pooling mode: 就类似于deepseek的3fs这种
      - p2p mode
    - How to extract (and inject) KV cache from (to) vLLM?
      - connector API
      - before model forward: try receive kv cache(inject kv cache into vLLM's paged memory)`self.need_recv_kv` 这个代码是去接受KV vllm/vllm/worker/model_runner.py
      - model forward
      - after model forward: extract kv cache from vLLM's paged memory and send it to outside `self.need_send_kv` 这个代码是去发送KV vllm/vllm/worker/model_runner.py
      vllm/vllm/distributed/kv_transfer/kv_connector
    - When to send teh request to P and D node?

## EP4

* Speculative decoding(LLM inference is GPU-memory-bound) 这个难度很大。。。
- Find a way to increase amount of computation but does not significantly increase the amount of GPU memory access.

* Solution: Generate token --> Guess multiple tokens and verify
- In terms of token generation per iteration
  - Guess 3 token, acceptance rate 2/3
  - 2 tokens of guessing is correct, LLM inference will generate a new token --> 3 tokens
- Iteration time
  - Computation: (1 + 3)x
  - Memory:
    - w/o spec: Model parameters (8x2 GB) + KV caches (n * 100 KB)
    - w/ spec: Model parameters (8x2 GB) + KV caches ((n+3) * 100 KB)
  - Iteration time almost unchanged

* A metric to measure whether it's computation-bound or memory-bound: arithmetic intensity
- Definition: FLOPs (floating point per second) / MIPs (memory instructions) 就是一个GPU有一个最佳的运行指标，通过FLOPS除以MIPs

Optimizing Speculative Decoding for Serving Large Language Models Using Goodput: https://arxiv.org/abs/2406.14066


 
## EP5

### Prefix Caching

```python
class KVCacheStorage:
    def put(self, tokens, kv_cache_tensor):
        pass
    
    def get(self, tokens) -> kv_cache_tensor:
        pass
```
和传统的KV不一样的是它有一个 `prefix-based matching` 的概念。
Tokens 1: ABCDE -> [KV1, KV2, KV3, KV4, KV5]
Tokens 2: ABCDF -> [KV1, KV2, KV3, KV4, KV6]

kv_cache_store.put("ABCDE", [KV1, KV2, KV3, KV4, KV5])")
kv_cache_store.put("ABCDF", [KV1, KV2, KV3, KV4, KV6])

它是一个经典的字符串最长前缀匹配问题。典型的做法是用 Trie 树来做，但是实现比较复杂，所以要简化实现：
"ABCDEF" -> "AB","CD","EF" -> list of chunked prefix hashes (核心思想就是把一个长的字符串分成几个小的字符串，然后用hash来表示这个小的字符串)
```python
prefix_hash = ""
for chunk in chunked_tokens: #["AB","CD","EF"],把key 分成 chunk 块，再来计算hash
    chunk_hash = hash(prefix_hash + chunk)
    prefix_hash = chunk_hash

# 比如后面对接一个redis
# ? chunked_hashes 和 chunked_kv 是什么，为什么需要进过zip处理
for chunk_hash,chunk_kv in zip(chunked_hashes, chunked_kv):
    redis.put(chunk_hash, chunk_kv)

for chunk_hash in chunked_hashes:
    redis.get(chunk_hash)
```

LMCache 就是基于这个顶级抽象实现的，可以去参考对应的代码。


核心代码在：`get_computed_blocks` vllm/v1/core/sched/scheduler.py
缓存驱逐的时候：`_maybe_evict_cached_block` vllm/v1/core/block_pool.py
```python

1. block_size 默认值问题？
2. 