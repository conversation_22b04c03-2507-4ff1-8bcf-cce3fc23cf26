# Why v1?
- vLLM v0 is a bit slow (cpu overhead,but gpu is 空闲)
- vLLM v0 code is hard to read and develop(v0开发代码很难)



vLLM most import feature: new model support!!!
- candidate: easy-to-use
- candidate: performance


Why Pytorch wins Tensorflow?
- 因为之前最还是在学校里面research的那批人用Pytorch，当他们毕业之后进入公司之后，并不想改变，所以慢慢的就改变了影响。 

# Scheduler:
- Scheduling: how many extra tokens do we schedule for each request compared to last time(与上次相比，我们为每个请求安排了多少额外的令牌)
  - Request r, length 500
  - Prefill: (r: 500)
  - Decode: (r: 1)
  - Chunked prefill
  - Prefix caching
  - Speculative decoding
  - Multi-modality
https://docs.google.com/presentation/d/16T2PDD1YwRnZ4Tu8Q5r6n53c5Lr5c73UV9Vd2_eBo4U/edit#slide=id.g327d4a64f8b_0_95
- Simple Scheduler: chunked prefill by default
- Synced Scheduler

# Worker
- Persistent batching
- Piecewise cudagraph

# Attention kernel
- simple configuration
- cascade inference

# General architecture
- Scheduler, API server,(de)tokenizer in separate process

# Multi-modal
- Embedding as the KV cache reference
- KV cachemanagement(incoming)
  - Hybrid memory allocator



1. 调度简化：https://docs.google.com/presentation/d/16T2PDD1YwRnZ4Tu8Q5r6n53c5Lr5c73UV9Vd2_eBo4U/edit#slide=id.g327d4a64f8b_0_95
2. 优化执行循环，把request和scheduler分开在两个process，使用zeromq进行通信：https://blog.vllm.ai/2025/01/27/v1-alpha-release.html
3. Scheduler 和 Worker process 分离。（SPMD？SPMD(Single Program, Multiple Data)是一种并行程序设计的模型,主要思想是使用同一个程序在多个处理器上并行执行,但每个处理器执行程序时处理不同的数据）
4. Persistent batching: (做CPU和GPU之间的差量处理，减少CPU和gpu上的传输)
   - vllm/v1/worker/gpu_input_batch.py
   - vllm/v1/worker/gpu_worker.py
5. CudaGraph：记录一个 series CUDA kernel operations & replay after then.(核心是因为cpu到用cuda kernel的时间是很长，所以把它记录下来，下次只需要和它说重新执行某一个cuda kernel就行)
   - 它不会录制 cpu operation
- Observations: flexibiliy typically happens in attention layer not in MLP layer.

6. Attention kernel
- Cascade inference
    - 大大的简化了 System Prompt 的使用，比如
    - System Prompt 有 10000 token，有10个用户，每个聊天 100 token
    - Normal attention：内存占用为 （10000+100） * 10
    - Cascade attention：内存占用为 10000 + 100 * 10
    - 代码位置：vllm/v1/attention/backends/flash_attn.py (use_cascade_attention)

